/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #1a1a1a;
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 700;
    color: #d4af37;
}

.logo {
    width: 40px;
    height: 40px;
    background: #d4af37;
    border-radius: 8px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #d4af37;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #ffffff;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

.hero-content {
    text-align: center;
    z-index: 2;
}

.hero-title {
    font-family: 'Playfair Display', serif;
    font-size: 4rem;
    font-weight: 700;
    color: #d4af37;
    margin-bottom: 1rem;
    letter-spacing: 2px;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: #cccccc;
    margin-bottom: 3rem;
    font-weight: 300;
}

.hero-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.btn-primary {
    background: rgba(212, 175, 55, 0.2);
    border: 2px solid #d4af37;
    color: #d4af37;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-primary:hover {
    background: #d4af37;
    color: #1a1a1a;
}

.btn-secondary {
    color: #ffffff;
    text-decoration: none;
    font-size: 1.1rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid transparent;
    transition: border-color 0.3s ease;
}

.btn-secondary:hover {
    border-bottom-color: #d4af37;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="%23d4af37" opacity="0.1"/></svg>');
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* About Section */
.about {
    padding: 8rem 0;
    background: #222222;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about h2 {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    color: #d4af37;
    margin-bottom: 2rem;
}

.divider {
    width: 100px;
    height: 2px;
    background: #d4af37;
    margin-bottom: 2rem;
}

.about p {
    font-size: 1.2rem;
    line-height: 1.8;
    color: #cccccc;
}

.about-logo {
    display: flex;
    justify-content: center;
}

.logo-container {
    width: 300px;
    height: 300px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-bg {
    width: 200px;
    height: 200px;
    background: #d4af37;
    border-radius: 20px;
    position: relative;
    transform: rotate(45deg);
}

.logo-bg::before {
    content: 'R';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    font-family: 'Playfair Display', serif;
    font-size: 4rem;
    font-weight: 700;
    color: #1a1a1a;
}

/* Brands Section */
.brands {
    padding: 8rem 0;
    background: #1a1a1a;
}

.brands h2 {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    color: #d4af37;
    text-align: center;
    margin-bottom: 4rem;
}

.brands-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.brand-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: transform 0.3s ease, background 0.3s ease;
    border: 1px solid rgba(212, 175, 55, 0.2);
}

.brand-item:hover {
    transform: translateY(-10px);
    background: rgba(212, 175, 55, 0.1);
}

.brand-logo {
    font-size: 1.2rem;
    font-weight: 600;
    color: #d4af37;
}

/* Services Section */
.services {
    padding: 8rem 0;
    background: #222222;
}

.services-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: center;
}

.services-icon {
    display: flex;
    justify-content: center;
}

.camera-icon {
    width: 200px;
    height: 200px;
    background: #333333;
    border-radius: 50%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.camera-icon::before {
    content: '📹';
    font-size: 4rem;
}

.service-item {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border-left: 4px solid #d4af37;
}

.service-item h3 {
    color: #d4af37;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.service-item p {
    color: #cccccc;
    line-height: 1.6;
}

/* Contact Section */
.contact {
    padding: 8rem 0;
    background: #1a1a1a;
    text-align: center;
}

.contact h2 {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    color: #d4af37;
    margin-bottom: 1rem;
}

.contact p {
    font-size: 1.2rem;
    color: #cccccc;
    margin-bottom: 3rem;
}

.contact-form {
    max-width: 600px;
    margin: 0 auto;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 1rem;
    margin-bottom: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
}

.contact-form input::placeholder,
.contact-form textarea::placeholder {
    color: #999999;
}

/* Footer */
.footer {
    background: #111111;
    padding: 2rem 0;
    text-align: center;
    color: #666666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        display: none;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
    }
    
    .about-content,
    .services-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .brands-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}
