/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #1a1a1a;
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
    cursor: none;
}

/* Custom Cursor */
.cursor {
    width: 20px;
    height: 20px;
    border: 2px solid #d4af37;
    border-radius: 50%;
    position: fixed;
    pointer-events: none;
    z-index: 9999;
    transition: all 0.1s ease;
    mix-blend-mode: difference;
}

.cursor-follower {
    width: 8px;
    height: 8px;
    background: #d4af37;
    border-radius: 50%;
    position: fixed;
    pointer-events: none;
    z-index: 9998;
    transition: all 0.15s ease;
}

/* Scroll Progress */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 4px;
    background: linear-gradient(90deg, #d4af37, #f4d03f);
    z-index: 9999;
    transition: width 0.1s ease;
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    transition: opacity 0.5s ease;
}

.loading-content {
    text-align: center;
}

.loading-logo {
    width: 80px;
    height: 80px;
    background: #d4af37;
    border-radius: 15px;
    margin: 0 auto 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Playfair Display', serif;
    font-size: 2rem;
    font-weight: 700;
    color: #1a1a1a;
    animation: pulse 2s infinite;
}

.loading-text {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    color: #d4af37;
    margin-bottom: 1rem;
}

.loading-bar {
    width: 200px;
    height: 3px;
    background: rgba(212, 175, 55, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto;
}

.loading-progress {
    width: 0%;
    height: 100%;
    background: linear-gradient(90deg, #d4af37, #f4d03f);
    border-radius: 2px;
    animation: loading 3s ease-in-out forwards;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes loading {
    0% { width: 0%; }
    100% { width: 100%; }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 700;
    color: #d4af37;
}

.logo {
    width: 40px;
    height: 40px;
    background: #d4af37;
    border-radius: 8px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #d4af37;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #ffffff;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23d4af37" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(10px, 10px); }
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #d4af37;
    border-radius: 50%;
    opacity: 0.6;
    animation: float-particle 6s infinite ease-in-out;
}

@keyframes float-particle {
    0%, 100% {
        transform: translateY(0px) translateX(0px);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-20px) translateX(10px);
        opacity: 1;
    }
}

.hero-content {
    text-align: center;
    z-index: 2;
}

.hero-title {
    font-family: 'Playfair Display', serif;
    font-size: 4rem;
    font-weight: 700;
    color: #d4af37;
    margin-bottom: 1rem;
    letter-spacing: 2px;
    text-shadow: 0 0 30px rgba(212, 175, 55, 0.5);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { text-shadow: 0 0 30px rgba(212, 175, 55, 0.5); }
    to { text-shadow: 0 0 40px rgba(212, 175, 55, 0.8), 0 0 60px rgba(212, 175, 55, 0.3); }
}

.hero-subtitle {
    font-size: 1.5rem;
    color: #cccccc;
    margin-bottom: 3rem;
    font-weight: 300;
}

.hero-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.btn-primary {
    background: rgba(212, 175, 55, 0.2);
    border: 2px solid #d4af37;
    color: #d4af37;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-primary:hover {
    background: #d4af37;
    color: #1a1a1a;
}

.btn-secondary {
    color: #ffffff;
    text-decoration: none;
    font-size: 1.1rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid transparent;
    transition: border-color 0.3s ease;
}

.btn-secondary:hover {
    border-bottom-color: #d4af37;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="%23d4af37" opacity="0.1"/></svg>');
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* About Section */
.about {
    padding: 8rem 0;
    background: #222222;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about h2 {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    color: #d4af37;
    margin-bottom: 2rem;
}

.divider {
    width: 100px;
    height: 2px;
    background: #d4af37;
    margin-bottom: 2rem;
}

.about p {
    font-size: 1.2rem;
    line-height: 1.8;
    color: #cccccc;
}

.about-logo {
    display: flex;
    justify-content: center;
}

.logo-container {
    width: 300px;
    height: 300px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-bg {
    width: 200px;
    height: 200px;
    background: #d4af37;
    border-radius: 20px;
    position: relative;
    transform: rotate(45deg);
}

.logo-bg::before {
    content: 'R';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    font-family: 'Playfair Display', serif;
    font-size: 4rem;
    font-weight: 700;
    color: #1a1a1a;
}

/* Brands Section */
.brands {
    padding: 8rem 0;
    background: #1a1a1a;
    position: relative;
    overflow: hidden;
}

.brands::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="%23d4af37" stroke-width="0.5" opacity="0.05"/></svg>');
    background-size: 400px 400px;
    animation: rotate 60s linear infinite;
    opacity: 0.3;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.brands h2 {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    color: #d4af37;
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
}

.brands h2::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, transparent, #d4af37, transparent);
}

.brands-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
    position: relative;
    z-index: 2;
}

.brand-item {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 1px solid rgba(212, 175, 55, 0.1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.brand-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.brand-item:hover {
    transform: translateY(-15px) scale(1.05);
    border-color: rgba(212, 175, 55, 0.3);
    box-shadow: 0 15px 40px rgba(212, 175, 55, 0.1);
}

.brand-item:hover::before {
    opacity: 1;
}

.brand-logo {
    position: relative;
    z-index: 2;
    transition: transform 0.3s ease;
}

.brand-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    transition: transform 0.5s ease;
}

.brand-item:hover .brand-icon {
    transform: scale(1.2);
}

.brand-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: #d4af37;
    margin-bottom: 0.5rem;
}

.brand-category {
    font-size: 0.9rem;
    color: #999;
    font-weight: 300;
}

/* Brand Stats */
.brands-stats {
    display: flex;
    justify-content: space-around;
    margin-top: 5rem;
    padding-top: 3rem;
    border-top: 1px solid rgba(212, 175, 55, 0.1);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: #d4af37;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: #cccccc;
    font-weight: 300;
}

/* Services Section */
.services {
    padding: 8rem 0;
    background: #222222;
    position: relative;
}

.services h2 {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    color: #d4af37;
    text-align: center;
    margin-bottom: 4rem;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.service-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 1px solid rgba(212, 175, 55, 0.1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    cursor: pointer;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.service-card:hover {
    transform: translateY(-20px) scale(1.02);
    border-color: rgba(212, 175, 55, 0.3);
    box-shadow: 0 20px 50px rgba(212, 175, 55, 0.15);
}

.service-card:hover::before {
    opacity: 1;
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    transition: transform 0.5s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.2) rotateY(360deg);
}

.service-card h3 {
    font-size: 1.5rem;
    color: #d4af37;
    margin-bottom: 1rem;
    font-weight: 600;
}

.service-card p {
    color: #cccccc;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.service-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    margin-bottom: 2rem;
}

.service-features span {
    background: rgba(212, 175, 55, 0.1);
    color: #d4af37;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    border: 1px solid rgba(212, 175, 55, 0.2);
}

.service-btn {
    background: transparent;
    border: 2px solid #d4af37;
    color: #d4af37;
    padding: 0.8rem 2rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
    z-index: 2;
}

.service-btn:hover {
    background: #d4af37;
    color: #1a1a1a;
    transform: scale(1.05);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    margin: 5% auto;
    padding: 2rem;
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 20px;
    width: 90%;
    max-width: 800px;
    position: relative;
    animation: modalSlideIn 0.5s ease;
}

@keyframes modalSlideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.close {
    color: #d4af37;
    float: right;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #f4d03f;
}

.modal-header h2 {
    color: #d4af37;
    font-family: 'Playfair Display', serif;
    margin-bottom: 1rem;
}

.portfolio-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.portfolio-item {
    aspect-ratio: 16/9;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(212, 175, 55, 0.2);
}

.portfolio-placeholder {
    color: #d4af37;
    font-weight: 500;
}

.service-details h3 {
    color: #d4af37;
    margin-bottom: 1rem;
}

.service-details ul {
    list-style: none;
    padding: 0;
}

.service-details li {
    color: #cccccc;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.service-details li::before {
    content: '✓';
    color: #d4af37;
    margin-right: 0.5rem;
}

/* Contact Section */
.contact {
    padding: 8rem 0;
    background: #1a1a1a;
    text-align: center;
}

.contact h2 {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    color: #d4af37;
    margin-bottom: 1rem;
}

.contact p {
    font-size: 1.2rem;
    color: #cccccc;
    margin-bottom: 3rem;
}

.contact-form {
    max-width: 600px;
    margin: 0 auto;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 1rem;
    margin-bottom: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
}

.contact-form input::placeholder,
.contact-form textarea::placeholder {
    color: #999999;
}

/* Footer */
.footer {
    background: #111111;
    padding: 2rem 0;
    text-align: center;
    color: #666666;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #d4af37, #f4d03f);
    border: none;
    border-radius: 50%;
    color: #1a1a1a;
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        cursor: auto;
    }

    .cursor,
    .cursor-follower {
        display: none;
    }

    .hamburger {
        display: flex;
    }

    .nav-menu {
        display: none;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 1rem;
    }

    .btn-primary {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .about h2 {
        font-size: 2.5rem;
    }

    .brands-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .brands-stats {
        flex-direction: column;
        gap: 2rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .service-card {
        padding: 2rem;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
        padding: 1.5rem;
    }

    .portfolio-gallery {
        grid-template-columns: 1fr;
    }

    .contact-form {
        padding: 0 1rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .about h2,
    .brands h2,
    .services h2,
    .contact h2 {
        font-size: 2rem;
    }

    .brands-grid {
        grid-template-columns: 1fr;
    }

    .service-card {
        padding: 1.5rem;
    }

    .nav-logo {
        font-size: 1.2rem;
    }

    .logo {
        width: 30px;
        height: 30px;
    }
}
